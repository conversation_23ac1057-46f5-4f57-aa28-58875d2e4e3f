import requests

# 多个 API Key，字符串格式，用换行符分割
api_keys_str = """
sk-ypvslubqogwenhpxtagdewxnfioelcslqzouxjmuddnlfags
sk-rlzpbgwgqezekkiafbxijbeitqguijzxndyhwjzvfaugwcdq
sk-yijmzxwasszfejlokpsbgpxmlvzvcnyebbfcyxhspzxxnjwy
sk-qvnostptpihnsjjcqlszwzlalcaawdnqftbpnzrhpprgniyd
sk-xpcfjbzywkeuwrwcvrgmupfvnzrkaspnjenhpmjgxfhmsawe
sk-shbfwwixcxqjydxnoaemuwjqhtsqezbvfefjzqnpldpljkfc
sk-sirwuxcbwgyfdlsqncmupihlxbkwvycyffkvptizuqbgvvuz
sk-zlkpykdsaclpkvqjocikiuzszatmkjlphujhwzjdwnjyaxtq
sk-agauaykbqugqowvvvkbqtcyomqjrxavztzoepeztvmzhdlwv
sk-dtiakpgptczwtauobjgokvrvuhltdsucdroipzjxbjhjcxdz
"""

# 将字符串按行拆分成列表
api_keys = api_keys_str.splitlines()

# API 请求地址
url = "https://api.siliconflow.cn/v1/user/info"

# 遍历每个 API Key 并获取余额
for key in api_keys:
    key = key.strip()  # 去除可能的空格或换行符
    if not key:  # 跳过空行
        continue
    
    headers = {"Authorization": f"Bearer {key}"}
    
    try:
        response = requests.get(url, headers=headers)
        data = response.json()
        
        # 检查响应是否有效
        if data.get("code") == 20000 and "data" in data:
            balance = data["data"].get("balance", "N/A")
            print(f"API Key: {key[:10]}... -> Balance: {balance}")
        else:
            print(f"API Key: {key[:10]}... -> 请求失败: {data.get('message', '未知错误')}")
    
    except Exception as e:
        print(f"API Key: {key[:10]}... -> 发生错误: {str(e)}")
