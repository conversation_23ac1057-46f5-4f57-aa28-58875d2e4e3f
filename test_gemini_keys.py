#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Gemini API密钥有效性测试脚本
测试多个API密钥是否有效，并通过代理服务器进行连接
增强版：包含详细的网络诊断功能
"""

import requests
import json
import time
import socket
import urllib.parse
from typing import List, Dict, Tuple


class GeminiKeyTester:
    def __init__(self, proxy_url: str = "http://127.0.0.1:10809"):
        """
        初始化Gemini密钥测试器

        Args:
            proxy_url: 代理服务器URL
        """
        self.proxy_url = proxy_url
        self.proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"

    def check_network_connectivity(self) -> Tuple[bool, str]:
        """
        检查网络连接状态

        Returns:
            Tuple[bool, str]: (连接状态, 详细信息)
        """
        print("🔍 正在进行网络连接诊断...")

        # 1. 检查代理服务器连接
        try:
            proxy_parts = urllib.parse.urlparse(self.proxy_url)
            proxy_host = proxy_parts.hostname
            proxy_port = proxy_parts.port

            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((proxy_host, proxy_port))
            sock.close()

            if result != 0:
                return False, f"❌ 代理服务器 {proxy_host}:{proxy_port} 无法连接"
            else:
                print(f"✅ 代理服务器 {proxy_host}:{proxy_port} 连接正常")
        except Exception as e:
            return False, f"❌ 代理服务器检查失败: {str(e)}"

        # 2. 测试通过代理访问Google服务
        try:
            test_url = "https://www.google.com"
            response = requests.get(
                test_url,
                proxies=self.proxies,
                timeout=10,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            if response.status_code == 200:
                print("✅ 通过代理访问Google服务正常")
            else:
                return False, f"❌ 通过代理访问Google返回状态码: {response.status_code}"
        except Exception as e:
            return False, f"❌ 通过代理访问Google失败: {str(e)}"

        # 3. 测试Gemini API端点连接
        try:
            test_url = "https://generativelanguage.googleapis.com"
            response = requests.get(
                test_url,
                proxies=self.proxies,
                timeout=10,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            print(f"✅ Gemini API端点连接正常 (状态码: {response.status_code})")
        except Exception as e:
            return False, f"❌ Gemini API端点连接失败: {str(e)}"

        return True, "✅ 所有网络连接检查通过"

    def test_api_key(self, api_key: str) -> Tuple[bool, str]:
        """
        测试单个API密钥的有效性

        Args:
            api_key: 要测试的API密钥

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息或成功信息)
        """
        try:
            # 构建请求URL
            url = f"{self.base_url}?key={api_key}"

            # 构建测试请求数据
            test_data = {
                "contents": [{
                    "parts": [{
                        "text": "Hello, this is a test message. Please respond with 'API key is working'."
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 50
                }
            }

            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            print(f"🔑 正在测试密钥: {api_key[:20]}...")
            print(f"📡 请求URL: {url[:80]}...")

            # 记录请求开始时间
            start_time = time.time()

            # 发送请求
            response = requests.post(
                url,
                json=test_data,
                headers=headers,
                proxies=self.proxies,
                timeout=30
            )

            # 计算响应时间
            response_time = time.time() - start_time
            print(f"⏱️  响应时间: {response_time:.2f}秒")
            print(f"📊 HTTP状态码: {response.status_code}")

            # 打印响应头信息（用于调试）
            print(
                f"📋 响应头: Content-Type={response.headers.get('content-type', 'N/A')}")

            # 检查响应状态
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"📄 响应内容预览: {str(result)[:200]}...")

                    if 'candidates' in result and len(result['candidates']) > 0:
                        generated_text = result['candidates'][0]['content']['parts'][0]['text']
                        return True, f"✅ 成功 - 响应: {generated_text[:50]}..."
                    else:
                        return False, f"❌ API响应格式异常: {result}"
                except json.JSONDecodeError as e:
                    print(f"📄 原始响应内容: {response.text[:200]}...")
                    return False, f"❌ JSON解析错误: {str(e)}"

            elif response.status_code == 400:
                try:
                    error_info = response.json()
                    error_message = error_info.get(
                        'error', {}).get('message', '未知错误')
                    print(f"📄 400错误详情: {error_info}")
                    return False, f"❌ 请求错误 (400): {error_message}"
                except:
                    print(f"📄 原始400错误响应: {response.text[:200]}...")
                    return False, f"❌ 请求错误 (400): {response.text[:100]}"

            elif response.status_code == 403:
                try:
                    error_info = response.json()
                    print(f"📄 403错误详情: {error_info}")
                    error_message = error_info.get(
                        'error', {}).get('message', 'API密钥无效或权限不足')
                    return False, f"❌ 权限错误 (403): {error_message}"
                except:
                    print(f"📄 原始403错误响应: {response.text[:200]}...")
                    return False, "❌ API密钥无效或权限不足 (403)"

            elif response.status_code == 429:
                return False, "❌ 请求频率限制 (429) - 请稍后重试"

            else:
                print(f"📄 未知状态码响应: {response.text[:200]}...")
                return False, f"❌ HTTP错误 ({response.status_code}): {response.text[:100]}"

        except requests.exceptions.ProxyError as e:
            return False, f"❌ 代理连接失败: {str(e)} - 请检查代理服务器是否运行"

        except requests.exceptions.ConnectTimeout as e:
            return False, f"❌ 连接超时: {str(e)} - 请检查网络连接"

        except requests.exceptions.ReadTimeout as e:
            return False, f"❌ 读取超时: {str(e)} - 服务器响应过慢"

        except requests.exceptions.SSLError as e:
            return False, f"❌ SSL错误: {str(e)} - 可能是证书问题"

        except requests.exceptions.RequestException as e:
            return False, f"❌ 网络请求错误: {str(e)}"

        except Exception as e:
            return False, f"❌ 未知错误: {str(e)}"

    def test_multiple_keys(self, api_keys: List[str]) -> Dict[str, Tuple[bool, str]]:
        """
        测试多个API密钥

        Args:
            api_keys: API密钥列表

        Returns:
            Dict[str, Tuple[bool, str]]: 密钥测试结果字典
        """
        results = {}

        print(f"🚀 开始测试 {len(api_keys)} 个API密钥...")
        print(f"🌐 使用代理: {self.proxy_url}")
        print("=" * 60)

        # 首先进行网络连接检查
        network_ok, network_msg = self.check_network_connectivity()
        if not network_ok:
            print(f"\n⚠️  网络连接检查失败: {network_msg}")
            print("❌ 由于网络问题，无法继续测试API密钥")
            return {}

        print(f"\n{network_msg}")
        print("\n" + "=" * 60)
        print("🔑 开始逐个测试API密钥...")

        for i, key in enumerate(api_keys, 1):
            print(f"\n{'='*20} [{i}/{len(api_keys)}] {'='*20}")
            print(f"🔍 测试密钥: {key[:20]}...")

            is_valid, message = self.test_api_key(key)
            results[key] = (is_valid, message)

            # 显示即时结果
            status = "✅ 有效" if is_valid else "❌ 无效"
            print(f"📊 最终结果: {status}")
            print(f"📝 详细信息: {message}")

            # 避免请求过于频繁
            if i < len(api_keys):
                print("⏳ 等待1秒后继续...")
                time.sleep(1)

        return results

    def print_summary(self, results: Dict[str, Tuple[bool, str]]):
        """
        打印测试结果摘要

        Args:
            results: 测试结果字典
        """
        print("\n" + "=" * 60)
        print("📊 测试结果摘要")
        print("=" * 60)

        valid_keys = []
        invalid_keys = []

        for key, (is_valid, message) in results.items():
            if is_valid:
                valid_keys.append(key)
            else:
                invalid_keys.append((key, message))

        print(f"\n✅ 有效密钥数量: {len(valid_keys)}")
        for key in valid_keys:
            print(f"   • {key}")

        print(f"\n❌ 无效密钥数量: {len(invalid_keys)}")
        for key, error in invalid_keys:
            print(f"   • {key}")
            print(f"     错误: {error}")

        print(f"\n📈 总体统计:")
        print(f"   • 总密钥数: {len(results)}")
        print(f"   • 有效密钥: {len(valid_keys)}")
        print(f"   • 无效密钥: {len(invalid_keys)}")
        print(f"   • 成功率: {len(valid_keys)/len(results)*100:.1f}%")


def main():
    """主函数"""
    # 要测试的API密钥列表（字符串格式）
    api_keys_str = """
AIzaSyCwEnHOcw-0bAIytN-sNCTsCIqxS1T454k
AIzaSyCAtcc9mgVJQ3_WGeZ89ZkOK6EaQFK6FS4
AIzaSyCmF4NA7QEoylU_oSROWygBfmOdujuX4cA
AIzaSyDDLn_YeC_LtuYqlP3Dz31-f11k69PTuys
"""

    # 将字符串按行拆分成列表，并过滤空行
    api_keys = [key.strip()
                for key in api_keys_str.strip().split('\n') if key.strip()]

    print("🔑 Google Gemini API密钥有效性测试工具")
    print("=" * 60)

    # 创建测试器实例
    tester = GeminiKeyTester(proxy_url="http://127.0.0.1:10809")

    try:
        # 执行测试
        results = tester.test_multiple_keys(api_keys)

        # 打印摘要
        tester.print_summary(results)

    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序执行出错: {str(e)}")


if __name__ == "__main__":
    main()
