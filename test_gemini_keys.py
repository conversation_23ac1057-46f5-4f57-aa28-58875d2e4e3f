#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Gemini API密钥有效性测试脚本
测试多个API密钥是否有效，并通过代理服务器进行连接
"""

import requests
import json
import time
from typing import List, Dict, Tuple

class GeminiKeyTester:
    def __init__(self, proxy_url: str = "http://127.0.0.1:10809"):
        """
        初始化Gemini密钥测试器
        
        Args:
            proxy_url: 代理服务器URL
        """
        self.proxy_url = proxy_url
        self.proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
        
    def test_api_key(self, api_key: str) -> Tuple[bool, str]:
        """
        测试单个API密钥的有效性
        
        Args:
            api_key: 要测试的API密钥
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息或成功信息)
        """
        try:
            # 构建请求URL
            url = f"{self.base_url}?key={api_key}"
            
            # 构建测试请求数据
            test_data = {
                "contents": [{
                    "parts": [{
                        "text": "Hello, this is a test message. Please respond with 'API key is working'."
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 50
                }
            }
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            print(f"正在测试密钥: {api_key[:20]}...")
            
            # 发送请求
            response = requests.post(
                url,
                json=test_data,
                headers=headers,
                proxies=self.proxies,
                timeout=30
            )
            
            # 检查响应状态
            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'candidates' in result and len(result['candidates']) > 0:
                        generated_text = result['candidates'][0]['content']['parts'][0]['text']
                        return True, f"成功 - 响应: {generated_text[:50]}..."
                    else:
                        return False, f"API响应格式异常: {result}"
                except json.JSONDecodeError:
                    return False, f"JSON解析错误: {response.text[:100]}"
                    
            elif response.status_code == 400:
                try:
                    error_info = response.json()
                    error_message = error_info.get('error', {}).get('message', '未知错误')
                    return False, f"请求错误 (400): {error_message}"
                except:
                    return False, f"请求错误 (400): {response.text[:100]}"
                    
            elif response.status_code == 403:
                return False, "API密钥无效或权限不足 (403)"
                
            elif response.status_code == 429:
                return False, "请求频率限制 (429)"
                
            else:
                return False, f"HTTP错误 ({response.status_code}): {response.text[:100]}"
                
        except requests.exceptions.ProxyError:
            return False, "代理连接失败 - 请检查代理服务器是否运行"
            
        except requests.exceptions.ConnectTimeout:
            return False, "连接超时 - 请检查网络连接"
            
        except requests.exceptions.RequestException as e:
            return False, f"网络请求错误: {str(e)}"
            
        except Exception as e:
            return False, f"未知错误: {str(e)}"
    
    def test_multiple_keys(self, api_keys: List[str]) -> Dict[str, Tuple[bool, str]]:
        """
        测试多个API密钥
        
        Args:
            api_keys: API密钥列表
            
        Returns:
            Dict[str, Tuple[bool, str]]: 密钥测试结果字典
        """
        results = {}
        
        print(f"开始测试 {len(api_keys)} 个API密钥...")
        print(f"使用代理: {self.proxy_url}")
        print("=" * 60)
        
        for i, key in enumerate(api_keys, 1):
            print(f"\n[{i}/{len(api_keys)}] 测试密钥: {key[:20]}...")
            
            is_valid, message = self.test_api_key(key)
            results[key] = (is_valid, message)
            
            # 显示即时结果
            status = "✅ 有效" if is_valid else "❌ 无效"
            print(f"结果: {status} - {message}")
            
            # 避免请求过于频繁
            if i < len(api_keys):
                time.sleep(1)
        
        return results
    
    def print_summary(self, results: Dict[str, Tuple[bool, str]]):
        """
        打印测试结果摘要
        
        Args:
            results: 测试结果字典
        """
        print("\n" + "=" * 60)
        print("📊 测试结果摘要")
        print("=" * 60)
        
        valid_keys = []
        invalid_keys = []
        
        for key, (is_valid, message) in results.items():
            if is_valid:
                valid_keys.append(key)
            else:
                invalid_keys.append((key, message))
        
        print(f"\n✅ 有效密钥数量: {len(valid_keys)}")
        for key in valid_keys:
            print(f"   • {key}")
        
        print(f"\n❌ 无效密钥数量: {len(invalid_keys)}")
        for key, error in invalid_keys:
            print(f"   • {key}")
            print(f"     错误: {error}")
        
        print(f"\n📈 总体统计:")
        print(f"   • 总密钥数: {len(results)}")
        print(f"   • 有效密钥: {len(valid_keys)}")
        print(f"   • 无效密钥: {len(invalid_keys)}")
        print(f"   • 成功率: {len(valid_keys)/len(results)*100:.1f}%")


def main():
    """主函数"""
    # 要测试的API密钥列表
    api_keys = [
        "AIzaSyCwEnHOcw-0bAIytN-sNCTsCIqxS1T454k",
        "AIzaSyCAtcc9mgVJQ3_WGeZ89ZkOK6EaQFK6FS4",
        "AIzaSyCmF4NA7QEoylU_oSROWygBfmOdujuX4cA",
        "AIzaSyDDLn_YeC_LtuYqlP3Dz31-f11k69PTuys"
    ]
    
    print("🔑 Google Gemini API密钥有效性测试工具")
    print("=" * 60)
    
    # 创建测试器实例
    tester = GeminiKeyTester(proxy_url="http://127.0.0.1:10809")
    
    try:
        # 执行测试
        results = tester.test_multiple_keys(api_keys)
        
        # 打印摘要
        tester.print_summary(results)
        
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 程序执行出错: {str(e)}")


if __name__ == "__main__":
    main()
